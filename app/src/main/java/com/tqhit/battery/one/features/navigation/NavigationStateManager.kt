package com.tqhit.battery.one.features.navigation

import android.util.Log
import androidx.fragment.app.Fragment
import com.tqhit.battery.one.R
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manages navigation state and synchronization.
 * Extracted from DynamicNavigationManager to improve maintainability and separation of concerns.
 * 
 * Responsibilities:
 * - Navigation state creation, updates, and validation
 * - State restoration from saved instance state
 * - State synchronization with actual fragment visibility
 * - State change event emission
 * - MVI pattern compliance for navigation state
 * 
 * Following stats module architecture pattern with proper dependency injection.
 */
@Singleton
class NavigationStateManager @Inject constructor() {
    companion object {
        private const val TAG = "NavigationStateManager"
    }

    private val _navigationState = MutableStateFlow<NavigationState?>(null)
    val navigationState: StateFlow<NavigationState?> = _navigationState.asStateFlow()

    private val _stateChanges = MutableStateFlow<NavigationStateChange?>(null)
    val stateChanges: StateFlow<NavigationStateChange?> = _stateChanges.asStateFlow()

    private var isInitialized = false

    /**
     * Initializes the navigation state manager.
     */
    fun initialize() {
        if (isInitialized) {
            Log.w(TAG, "NavigationStateManager already initialized, reinitializing")
            _navigationState.value = null
            _stateChanges.value = null
        }
        
        isInitialized = true
        Log.d(TAG, "NavigationStateManager initialized successfully")
    }

    /**
     * Sets up the initial navigation state based on current battery status and restored state.
     * 
     * @param isCharging Current battery charging state
     * @param restoredSelectedItemId The restored selected item ID from saved state (null for fresh start)
     */
    fun setupInitialState(isCharging: Boolean, restoredSelectedItemId: Int? = null) {
        Log.d(TAG, "NAVIGATION_RESTORE: Setting up initial state - charging: $isCharging, restored item: $restoredSelectedItemId")

        val initialState = if (restoredSelectedItemId != null) {
            // State restoration - create state based on restored item
            Log.d(TAG, "NAVIGATION_RESTORE: Creating state for restoration")
            when (restoredSelectedItemId) {
                R.id.chargeFragment -> NavigationState.createChargingState(shouldShowTransition = false)
                R.id.dischargeFragment -> NavigationState.createDischargingState(shouldShowTransition = false)
                R.id.animationGridFragment -> NavigationState.createAnimationState(isCharging, shouldShowTransition = false)
                R.id.healthFragment -> NavigationState.createAnimationState(isCharging, shouldShowTransition = false).copy(
                    activeFragmentId = R.id.healthFragment
                )
                R.id.settingsFragment -> NavigationState.createAnimationState(isCharging, shouldShowTransition = false).copy(
                    activeFragmentId = R.id.settingsFragment
                )
                else -> {
                    Log.w(TAG, "NAVIGATION_RESTORE: Unknown restored item $restoredSelectedItemId, falling back to animation state")
                    NavigationState.createAnimationState(isCharging, shouldShowTransition = false)
                }
            }
        } else {
            // Fresh start - use animation state as default
            Log.d(TAG, "NAVIGATION_RESTORE: Creating fresh animation state")
            NavigationState.createAnimationState(isCharging, shouldShowTransition = false)
        }

        updateNavigationState(initialState, StateChangeReason.INITIAL_SETUP)
    }

    /**
     * Updates the navigation state for battery charging changes.
     * 
     * @param isCharging New charging state
     * @param shouldPerformDynamicSwitch Whether dynamic fragment switching should occur
     * @param targetFragmentId Target fragment ID for dynamic switching (if applicable)
     */
    fun updateForBatteryStateChange(
        isCharging: Boolean,
        shouldPerformDynamicSwitch: Boolean,
        targetFragmentId: Int? = null
    ) {
        val currentState = _navigationState.value

        // Don't update if the state hasn't actually changed
        if (currentState?.isCharging == isCharging && !shouldPerformDynamicSwitch) {
            Log.v(TAG, "DYNAMIC_SWITCHING: Charging state unchanged and no dynamic switch needed, skipping update")
            return
        }

        Log.d(TAG, "DYNAMIC_SWITCHING: Updating state for battery change - charging: $isCharging, dynamicSwitch: $shouldPerformDynamicSwitch")

        val newState = if (shouldPerformDynamicSwitch && targetFragmentId != null) {
            // User is on charge/discharge fragment - switch to the appropriate one
            createDynamicSwitchState(currentState, isCharging, targetFragmentId)
        } else {
            // User is on other fragments - update charging state but keep current fragment
            currentState?.copy(isCharging = isCharging) ?: if (isCharging) {
                NavigationState.createChargingState()
            } else {
                NavigationState.createDischargingState()
            }
        }

        val reason = if (isCharging) StateChangeReason.CHARGING_STARTED else StateChangeReason.CHARGING_STOPPED

        if (shouldPerformDynamicSwitch) {
            Log.i(TAG, "DYNAMIC_SWITCHING: Performing automatic fragment switch from ${getFragmentName(currentState?.activeFragmentId)} to ${getFragmentName(newState.activeFragmentId)}")
        } else {
            Log.d(TAG, "DYNAMIC_SWITCHING: Updating charging state without fragment switch - staying on ${getFragmentName(currentState?.activeFragmentId)}")
        }

        updateNavigationState(newState, reason)
    }

    /**
     * Updates the navigation state for user navigation.
     * 
     * @param targetFragmentId The target fragment ID
     * @param isCharging Current charging state
     * @param isChargeDischargeFragment Whether navigating to charge/discharge fragment
     */
    fun updateForUserNavigation(
        targetFragmentId: Int,
        isCharging: Boolean,
        isChargeDischargeFragment: Boolean = false
    ) {
        val currentState = _navigationState.value ?: run {
            Log.e(TAG, "No current navigation state available for user navigation")
            return
        }

        val newState = if (isChargeDischargeFragment) {
            // Special handling for charge/discharge fragments to ensure proper state synchronization
            currentState.copy(
                activeFragmentId = targetFragmentId,
                isCharging = isCharging, // Use actual battery status
                shouldShowTransition = true
            )
        } else {
            // Create new state for user navigation (non-charge/discharge fragments)
            currentState.copy(
                activeFragmentId = targetFragmentId,
                shouldShowTransition = true
            )
        }

        Log.d(TAG, "NAVIGATION: User navigation to ${getFragmentName(targetFragmentId)}")
        updateNavigationState(newState, StateChangeReason.USER_NAVIGATION)
    }

    /**
     * Synchronizes navigation state with actual fragment visibility.
     * 
     * @param actualVisibleFragmentId The actually visible fragment ID
     */
    fun synchronizeWithActualState(actualVisibleFragmentId: Int) {
        val currentState = _navigationState.value ?: return

        if (actualVisibleFragmentId != currentState.activeFragmentId) {
            Log.w(TAG, "NAVIGATION_STATE_FIX: ⚠️ STATE DESYNC DETECTED!")
            Log.w(TAG, "NAVIGATION_STATE_FIX: State says: ${getFragmentName(currentState.activeFragmentId)}")
            Log.w(TAG, "NAVIGATION_STATE_FIX: Actually visible: ${getFragmentName(actualVisibleFragmentId)}")

            // Force state synchronization
            val correctedState = currentState.copy(activeFragmentId = actualVisibleFragmentId)
            _navigationState.value = correctedState

            Log.i(TAG, "NAVIGATION_STATE_FIX: ✅ State synchronized to match visible fragment: ${getFragmentName(actualVisibleFragmentId)}")
        } else {
            Log.d(TAG, "NAVIGATION_STATE_FIX: State is already synchronized")
        }
    }

    /**
     * Gets the current navigation state.
     */
    fun getCurrentState(): NavigationState? = _navigationState.value

    /**
     * Checks if the navigation state manager is initialized.
     */
    fun isInitialized(): Boolean = isInitialized

    /**
     * Creates a new navigation state for dynamic fragment switching.
     */
    private fun createDynamicSwitchState(
        currentState: NavigationState?,
        isCharging: Boolean,
        targetFragmentId: Int
    ): NavigationState {
        return NavigationState(
            activeFragmentId = targetFragmentId,
            visibleMenuItems = currentState?.visibleMenuItems ?: NavigationState.ALWAYS_VISIBLE_ITEMS,
            isCharging = isCharging,
            shouldShowTransition = true // Always show transition for dynamic switching
        )
    }

    /**
     * Updates the navigation state and applies changes to UI.
     */
    private fun updateNavigationState(newState: NavigationState, reason: StateChangeReason) {
        val previousState = _navigationState.value
        _navigationState.value = newState

        // Enhanced logging for dynamic switching
        val previousFragmentName = getFragmentName(previousState?.activeFragmentId)
        val newFragmentName = getFragmentName(newState.activeFragmentId)
        val isDynamicSwitch = reason == StateChangeReason.CHARGING_STARTED || reason == StateChangeReason.CHARGING_STOPPED

        if (isDynamicSwitch && previousState?.activeFragmentId != newState.activeFragmentId) {
            Log.i(TAG, "DYNAMIC_SWITCHING: State transition - $previousFragmentName → $newFragmentName (reason: $reason, charging: ${newState.isCharging})")
        } else {
            Log.d(TAG, "DYNAMIC_SWITCHING: Navigation state updated - ${newFragmentName} (charging: ${newState.isCharging}, reason: $reason)")
        }

        // Emit state change event
        val stateChange = NavigationStateChange(previousState, newState, reason)
        _stateChanges.value = stateChange
    }

    /**
     * Gets a human-readable fragment name for logging.
     */
    private fun getFragmentName(fragmentId: Int?): String {
        return when (fragmentId) {
            R.id.chargeFragment -> "ChargeFragment"
            R.id.dischargeFragment -> "DischargeFragment"
            R.id.animationGridFragment -> "AnimationGridFragment"
            R.id.othersFragment -> "OthersFragment"
            R.id.healthFragment -> "HealthFragment"
            R.id.settingsFragment -> "SettingsFragment"
            else -> "Unknown($fragmentId)"
        }
    }

    /**
     * Gets the fragment ID from a fragment instance.
     */
    fun getFragmentId(fragment: Fragment): Int? {
        return when (fragment) {
            is com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment -> R.id.animationGridFragment
            is com.tqhit.battery.one.fragment.main.others.OthersFragment -> R.id.othersFragment
            is com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment -> R.id.chargeFragment
            is com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment -> R.id.dischargeFragment
            is com.tqhit.battery.one.fragment.main.HealthFragment -> R.id.healthFragment
            is com.tqhit.battery.one.fragment.main.SettingsFragment -> R.id.settingsFragment
            else -> {
                Log.w(TAG, "Unknown fragment type: ${fragment.javaClass.simpleName}")
                null
            }
        }
    }
}
