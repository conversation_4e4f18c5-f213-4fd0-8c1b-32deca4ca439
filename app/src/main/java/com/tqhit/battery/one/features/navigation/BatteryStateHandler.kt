package com.tqhit.battery.one.features.navigation

import android.util.Log
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.tqhit.battery.one.R
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Handles battery state monitoring and dynamic switching logic.
 * Extracted from DynamicNavigationManager to improve maintainability and separation of concerns.
 * 
 * Responsibilities:
 * - Monitor CoreBatteryStatsService for charging state changes
 * - Determine when dynamic fragment switching should occur
 * - Emit battery state change events for navigation coordination
 * - Handle charging state transitions and validation
 * 
 * Following stats module architecture pattern with proper dependency injection.
 */
@Singleton
class BatteryStateHandler @Inject constructor(
    private val coreBatteryStatsProvider: CoreBatteryStatsProvider
) {
    companion object {
        private const val TAG = "BatteryStateHandler"
    }

    private val _batteryStateChanges = MutableSharedFlow<BatteryStateChange>()
    val batteryStateChanges: SharedFlow<BatteryStateChange> = _batteryStateChanges.asSharedFlow()

    private var isMonitoring = false
    private var lastKnownChargingState: Boolean? = null

    /**
     * Starts monitoring battery status changes and emits state change events.
     * 
     * @param lifecycleOwner The lifecycle owner for coroutine scope
     */
    fun startBatteryStatusMonitoring(lifecycleOwner: LifecycleOwner) {
        if (isMonitoring) {
            Log.w(TAG, "Battery status monitoring already started")
            return
        }

        Log.d(TAG, "Starting battery status monitoring")
        isMonitoring = true

        lifecycleOwner.lifecycleScope.launch {
            coreBatteryStatsProvider.coreBatteryStatusFlow
                .filterNotNull()
                .map { it.isCharging }
                .distinctUntilChanged()
                .collect { isCharging ->
                    Log.d(TAG, "Battery charging state changed: $isCharging")
                    handleChargingStateChange(isCharging)
                }
        }
    }

    /**
     * Stops battery status monitoring.
     */
    fun stopBatteryStatusMonitoring() {
        if (!isMonitoring) {
            Log.v(TAG, "Battery status monitoring already stopped")
            return
        }

        Log.d(TAG, "Stopping battery status monitoring")
        isMonitoring = false
        lastKnownChargingState = null
    }

    /**
     * Gets the current battery charging state.
     * 
     * @return Current charging state or null if unknown
     */
    fun getCurrentChargingState(): Boolean? {
        return try {
            val currentStatus = coreBatteryStatsProvider.getCurrentStatus()
            val isCharging = currentStatus?.isCharging
            Log.d(TAG, "Current charging state: $isCharging (battery: ${currentStatus?.percentage}%)")
            isCharging
        } catch (e: Exception) {
            Log.e(TAG, "Error getting current charging state", e)
            null
        }
    }

    /**
     * Determines if dynamic fragment switching should be performed based on current state.
     * 
     * @param currentFragmentId The currently active fragment ID
     * @param newChargingState The new charging state
     * @return true if dynamic switching should occur, false otherwise
     */
    fun shouldPerformDynamicFragmentSwitch(currentFragmentId: Int?, newChargingState: Boolean): Boolean {
        if (currentFragmentId == null) {
            Log.w(TAG, "DYNAMIC_SWITCHING: No current fragment ID available for switch analysis")
            return false
        }

        val currentFragmentName = getFragmentName(currentFragmentId)

        // Only switch if user is currently on charge or discharge fragment
        val isOnChargeDischargeFragment = currentFragmentId == R.id.chargeFragment || currentFragmentId == R.id.dischargeFragment

        Log.d(TAG, "DYNAMIC_SWITCHING: Switch analysis - currentFragment: $currentFragmentName, isOnChargeDischarge: $isOnChargeDischargeFragment")

        if (!isOnChargeDischargeFragment) {
            Log.v(TAG, "DYNAMIC_SWITCHING: User not on charge/discharge fragment ($currentFragmentName), no switch needed")
            return false
        }

        // Check if the current fragment matches the new charging state
        val shouldBeOnChargeFragment = newChargingState
        val isCurrentlyOnChargeFragment = currentFragmentId == R.id.chargeFragment

        val needsSwitch = shouldBeOnChargeFragment != isCurrentlyOnChargeFragment

        Log.i(TAG, "DYNAMIC_SWITCHING: Detailed analysis:")
        Log.i(TAG, "DYNAMIC_SWITCHING:   - Current fragment: $currentFragmentName (ID: $currentFragmentId)")
        Log.i(TAG, "DYNAMIC_SWITCHING:   - New charging state: $newChargingState")
        Log.i(TAG, "DYNAMIC_SWITCHING:   - Should be on charge fragment: $shouldBeOnChargeFragment")
        Log.i(TAG, "DYNAMIC_SWITCHING:   - Currently on charge fragment: $isCurrentlyOnChargeFragment")
        Log.i(TAG, "DYNAMIC_SWITCHING:   - Needs switch: $needsSwitch")

        return needsSwitch
    }

    /**
     * Creates a dynamic switch state for fragment switching.
     * 
     * @param isCharging The new charging state
     * @return The target fragment ID for the switch
     */
    fun createDynamicSwitchTarget(isCharging: Boolean): Int {
        val targetFragmentId = if (isCharging) R.id.chargeFragment else R.id.dischargeFragment
        Log.d(TAG, "DYNAMIC_SWITCHING: Created switch target - charging: $isCharging, target: ${getFragmentName(targetFragmentId)}")
        return targetFragmentId
    }

    /**
     * Handles charging state changes and emits appropriate events.
     */
    private fun handleChargingStateChange(isCharging: Boolean) {
        // Don't emit if the state hasn't actually changed
        if (lastKnownChargingState == isCharging) {
            Log.v(TAG, "DYNAMIC_SWITCHING: Charging state unchanged, skipping event emission")
            return
        }

        val previousState = lastKnownChargingState
        lastKnownChargingState = isCharging

        Log.d(TAG, "DYNAMIC_SWITCHING: Charging state changed from $previousState to $isCharging")

        // Emit battery state change event
        val stateChange = BatteryStateChange(
            previousChargingState = previousState,
            newChargingState = isCharging,
            timestamp = System.currentTimeMillis()
        )

        try {
            _batteryStateChanges.tryEmit(stateChange)
            Log.d(TAG, "DYNAMIC_SWITCHING: Emitted battery state change event")
        } catch (e: Exception) {
            Log.e(TAG, "Error emitting battery state change event", e)
        }
    }

    /**
     * Gets a human-readable fragment name for logging.
     */
    private fun getFragmentName(fragmentId: Int?): String {
        return when (fragmentId) {
            R.id.chargeFragment -> "ChargeFragment"
            R.id.dischargeFragment -> "DischargeFragment"
            R.id.animationGridFragment -> "AnimationGridFragment"
            R.id.othersFragment -> "OthersFragment"
            R.id.healthFragment -> "HealthFragment"
            R.id.settingsFragment -> "SettingsFragment"
            else -> "Unknown($fragmentId)"
        }
    }

    /**
     * Gets monitoring status for debugging.
     */
    fun getMonitoringStatus(): String {
        return "BatteryStateHandler - Monitoring: $isMonitoring, Last State: $lastKnownChargingState"
    }
}

/**
 * Represents a battery state change event.
 * 
 * @param previousChargingState The previous charging state (null if unknown)
 * @param newChargingState The new charging state
 * @param timestamp The timestamp when the change occurred
 */
data class BatteryStateChange(
    val previousChargingState: Boolean?,
    val newChargingState: Boolean,
    val timestamp: Long
)
