package com.tqhit.battery.one.features.navigation

import androidx.fragment.app.Fragment
import com.tqhit.battery.one.R
import com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment
import com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment
import com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment
import io.mockk.mockk
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

/**
 * Unit tests for NavigationStateManager.
 * Tests navigation state management, updates, and synchronization.
 */
class NavigationStateManagerTest {

    private lateinit var navigationStateManager: NavigationStateManager

    @Before
    fun setUp() {
        navigationStateManager = NavigationStateManager()
    }

    @Test
    fun `initialize sets up manager correctly`() {
        // Act
        navigationStateManager.initialize()

        // Assert
        assertTrue(navigationStateManager.isInitialized())
        assertNull(navigationStateManager.getCurrentState())
    }

    @Test
    fun `setupInitialState creates animation state for fresh start`() = runTest {
        // Arrange
        navigationStateManager.initialize()

        // Act
        navigationStateManager.setupInitialState(isCharging = true, restoredSelectedItemId = null)

        // Assert
        val state = navigationStateManager.getCurrentState()
        assertNotNull(state)
        assertEquals(R.id.animationGridFragment, state?.activeFragmentId)
        assertTrue(state?.isCharging == true)
        assertFalse(state?.shouldShowTransition == true)
    }

    @Test
    fun `setupInitialState restores charge fragment state`() = runTest {
        // Arrange
        navigationStateManager.initialize()

        // Act
        navigationStateManager.setupInitialState(isCharging = true, restoredSelectedItemId = R.id.chargeFragment)

        // Assert
        val state = navigationStateManager.getCurrentState()
        assertNotNull(state)
        assertEquals(R.id.animationGridFragment, state?.activeFragmentId) // Creates charging state which defaults to animation
        assertTrue(state?.isCharging == true)
    }

    @Test
    fun `setupInitialState handles unknown restored item`() = runTest {
        // Arrange
        navigationStateManager.initialize()

        // Act
        navigationStateManager.setupInitialState(isCharging = false, restoredSelectedItemId = 999)

        // Assert
        val state = navigationStateManager.getCurrentState()
        assertNotNull(state)
        assertEquals(R.id.animationGridFragment, state?.activeFragmentId)
        assertFalse(state?.isCharging == true)
    }

    @Test
    fun `updateForBatteryStateChange without dynamic switch updates charging state only`() = runTest {
        // Arrange
        navigationStateManager.initialize()
        navigationStateManager.setupInitialState(isCharging = false, restoredSelectedItemId = null)

        // Act
        navigationStateManager.updateForBatteryStateChange(
            isCharging = true,
            shouldPerformDynamicSwitch = false
        )

        // Assert
        val state = navigationStateManager.getCurrentState()
        assertNotNull(state)
        assertEquals(R.id.animationGridFragment, state?.activeFragmentId) // Fragment unchanged
        assertTrue(state?.isCharging == true) // Charging state updated
    }

    @Test
    fun `updateForBatteryStateChange with dynamic switch changes fragment`() = runTest {
        // Arrange
        navigationStateManager.initialize()
        navigationStateManager.setupInitialState(isCharging = false, restoredSelectedItemId = null)

        // Act
        navigationStateManager.updateForBatteryStateChange(
            isCharging = true,
            shouldPerformDynamicSwitch = true,
            targetFragmentId = R.id.chargeFragment
        )

        // Assert
        val state = navigationStateManager.getCurrentState()
        assertNotNull(state)
        assertEquals(R.id.chargeFragment, state?.activeFragmentId)
        assertTrue(state?.isCharging == true)
        assertTrue(state?.shouldShowTransition == true)
    }

    @Test
    fun `updateForUserNavigation updates fragment and transition flag`() = runTest {
        // Arrange
        navigationStateManager.initialize()
        navigationStateManager.setupInitialState(isCharging = false, restoredSelectedItemId = null)

        // Act
        navigationStateManager.updateForUserNavigation(
            targetFragmentId = R.id.healthFragment,
            isCharging = false,
            isChargeDischargeFragment = false
        )

        // Assert
        val state = navigationStateManager.getCurrentState()
        assertNotNull(state)
        assertEquals(R.id.healthFragment, state?.activeFragmentId)
        assertFalse(state?.isCharging == true)
        assertTrue(state?.shouldShowTransition == true)
    }

    @Test
    fun `updateForUserNavigation with charge discharge fragment syncs battery state`() = runTest {
        // Arrange
        navigationStateManager.initialize()
        navigationStateManager.setupInitialState(isCharging = false, restoredSelectedItemId = null)

        // Act
        navigationStateManager.updateForUserNavigation(
            targetFragmentId = R.id.chargeFragment,
            isCharging = true,
            isChargeDischargeFragment = true
        )

        // Assert
        val state = navigationStateManager.getCurrentState()
        assertNotNull(state)
        assertEquals(R.id.chargeFragment, state?.activeFragmentId)
        assertTrue(state?.isCharging == true) // Synced with actual battery state
        assertTrue(state?.shouldShowTransition == true)
    }

    @Test
    fun `synchronizeWithActualState corrects state desynchronization`() = runTest {
        // Arrange
        navigationStateManager.initialize()
        navigationStateManager.setupInitialState(isCharging = false, restoredSelectedItemId = null)

        // Act
        navigationStateManager.synchronizeWithActualState(R.id.healthFragment)

        // Assert
        val state = navigationStateManager.getCurrentState()
        assertNotNull(state)
        assertEquals(R.id.healthFragment, state?.activeFragmentId)
    }

    @Test
    fun `getFragmentId returns correct ID for known fragments`() {
        // Arrange
        val chargeFragment = StatsChargeFragment()
        val dischargeFragment = DischargeFragment()
        val animationFragment = AnimationGridFragment()

        // Act & Assert
        assertEquals(R.id.chargeFragment, navigationStateManager.getFragmentId(chargeFragment))
        assertEquals(R.id.dischargeFragment, navigationStateManager.getFragmentId(dischargeFragment))
        assertEquals(R.id.animationGridFragment, navigationStateManager.getFragmentId(animationFragment))
    }

    @Test
    fun `getFragmentId returns null for unknown fragments`() {
        // Arrange
        val unknownFragment = mockk<Fragment>()

        // Act
        val result = navigationStateManager.getFragmentId(unknownFragment)

        // Assert
        assertNull(result)
    }

    @Test
    fun `stateChanges flow emits state change events`() = runTest {
        // Arrange
        navigationStateManager.initialize()

        // Act
        navigationStateManager.setupInitialState(isCharging = true, restoredSelectedItemId = null)

        // Assert
        val stateChange = navigationStateManager.stateChanges.first()
        assertNotNull(stateChange)
        assertEquals(StateChangeReason.INITIAL_SETUP, stateChange?.reason)
        assertNull(stateChange?.previousState)
        assertNotNull(stateChange?.newState)
    }

    @Test
    fun `navigationState flow reflects current state`() = runTest {
        // Arrange
        navigationStateManager.initialize()

        // Act
        navigationStateManager.setupInitialState(isCharging = false, restoredSelectedItemId = null)

        // Assert
        val state = navigationStateManager.navigationState.first()
        assertNotNull(state)
        assertEquals(R.id.animationGridFragment, state?.activeFragmentId)
        assertFalse(state?.isCharging == true)
    }
}
