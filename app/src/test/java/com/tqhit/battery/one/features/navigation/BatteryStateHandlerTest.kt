package com.tqhit.battery.one.features.navigation

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.testing.TestLifecycleOwner
import com.tqhit.battery.one.R
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.features.stats.corebattery.domain.model.CoreBatteryStatus
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

/**
 * Unit tests for BatteryStateHandler.
 * Tests battery state monitoring, dynamic switching logic, and event emission.
 */
class BatteryStateHandlerTest {

    private lateinit var batteryStateHandler: BatteryStateHandler
    private lateinit var mockCoreBatteryStatsProvider: CoreBatteryStatsProvider
    private lateinit var mockBatteryStatusFlow: MutableSharedFlow<CoreBatteryStatus>
    private lateinit var testLifecycleOwner: TestLifecycleOwner

    @Before
    fun setUp() {
        mockCoreBatteryStatsProvider = mockk()
        mockBatteryStatusFlow = MutableSharedFlow()
        testLifecycleOwner = TestLifecycleOwner()

        every { mockCoreBatteryStatsProvider.coreBatteryStatusFlow } returns mockBatteryStatusFlow

        batteryStateHandler = BatteryStateHandler(mockCoreBatteryStatsProvider)
    }

    @Test
    fun `getCurrentChargingState returns correct charging state`() {
        // Arrange
        val mockStatus = mockk<CoreBatteryStatus> {
            every { isCharging } returns true
            every { percentage } returns 75
        }
        every { mockCoreBatteryStatsProvider.getCurrentStatus() } returns mockStatus

        // Act
        val result = batteryStateHandler.getCurrentChargingState()

        // Assert
        assertTrue(result == true)
        verify { mockCoreBatteryStatsProvider.getCurrentStatus() }
    }

    @Test
    fun `getCurrentChargingState returns null when status unavailable`() {
        // Arrange
        every { mockCoreBatteryStatsProvider.getCurrentStatus() } returns null

        // Act
        val result = batteryStateHandler.getCurrentChargingState()

        // Assert
        assertNull(result)
    }

    @Test
    fun `getCurrentChargingState handles exception gracefully`() {
        // Arrange
        every { mockCoreBatteryStatsProvider.getCurrentStatus() } throws RuntimeException("Test exception")

        // Act
        val result = batteryStateHandler.getCurrentChargingState()

        // Assert
        assertNull(result)
    }

    @Test
    fun `shouldPerformDynamicFragmentSwitch returns false for null fragment ID`() {
        // Act
        val result = batteryStateHandler.shouldPerformDynamicFragmentSwitch(null, true)

        // Assert
        assertFalse(result)
    }

    @Test
    fun `shouldPerformDynamicFragmentSwitch returns false for non-charge-discharge fragments`() {
        // Act
        val result = batteryStateHandler.shouldPerformDynamicFragmentSwitch(R.id.animationGridFragment, true)

        // Assert
        assertFalse(result)
    }

    @Test
    fun `shouldPerformDynamicFragmentSwitch returns true when switching from charge to discharge`() {
        // Act - Currently on charge fragment, battery is discharging
        val result = batteryStateHandler.shouldPerformDynamicFragmentSwitch(R.id.chargeFragment, false)

        // Assert
        assertTrue(result)
    }

    @Test
    fun `shouldPerformDynamicFragmentSwitch returns true when switching from discharge to charge`() {
        // Act - Currently on discharge fragment, battery is charging
        val result = batteryStateHandler.shouldPerformDynamicFragmentSwitch(R.id.dischargeFragment, true)

        // Assert
        assertTrue(result)
    }

    @Test
    fun `shouldPerformDynamicFragmentSwitch returns false when fragment matches charging state`() {
        // Act - Currently on charge fragment, battery is charging (no switch needed)
        val result = batteryStateHandler.shouldPerformDynamicFragmentSwitch(R.id.chargeFragment, true)

        // Assert
        assertFalse(result)
    }

    @Test
    fun `createDynamicSwitchTarget returns charge fragment for charging state`() {
        // Act
        val result = batteryStateHandler.createDynamicSwitchTarget(true)

        // Assert
        assertEquals(R.id.chargeFragment, result)
    }

    @Test
    fun `createDynamicSwitchTarget returns discharge fragment for discharging state`() {
        // Act
        val result = batteryStateHandler.createDynamicSwitchTarget(false)

        // Assert
        assertEquals(R.id.dischargeFragment, result)
    }

    @Test
    fun `startBatteryStatusMonitoring emits state changes`() = runTest {
        // Arrange
        batteryStateHandler.startBatteryStatusMonitoring(testLifecycleOwner)

        // Act
        val mockStatus = mockk<CoreBatteryStatus> {
            every { isCharging } returns true
        }
        mockBatteryStatusFlow.emit(mockStatus)

        // Assert
        val stateChange = batteryStateHandler.batteryStateChanges.first()
        assertTrue(stateChange.newChargingState)
        assertNull(stateChange.previousChargingState)
    }

    @Test
    fun `startBatteryStatusMonitoring filters duplicate states`() = runTest {
        // Arrange
        batteryStateHandler.startBatteryStatusMonitoring(testLifecycleOwner)

        // Act - Emit same charging state twice
        val mockStatus = mockk<CoreBatteryStatus> {
            every { isCharging } returns true
        }
        mockBatteryStatusFlow.emit(mockStatus)
        mockBatteryStatusFlow.emit(mockStatus) // Duplicate

        // Assert - Only one state change should be emitted
        val stateChange = batteryStateHandler.batteryStateChanges.first()
        assertTrue(stateChange.newChargingState)
    }

    @Test
    fun `stopBatteryStatusMonitoring resets state`() {
        // Arrange
        batteryStateHandler.startBatteryStatusMonitoring(testLifecycleOwner)

        // Act
        batteryStateHandler.stopBatteryStatusMonitoring()

        // Assert
        val status = batteryStateHandler.getMonitoringStatus()
        assertTrue(status.contains("Monitoring: false"))
        assertTrue(status.contains("Last State: null"))
    }

    @Test
    fun `getMonitoringStatus returns correct status`() {
        // Act
        val status = batteryStateHandler.getMonitoringStatus()

        // Assert
        assertTrue(status.contains("BatteryStateHandler"))
        assertTrue(status.contains("Monitoring:"))
        assertTrue(status.contains("Last State:"))
    }
}
